<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Certification;
use App\Models\Holiday;
use App\Models\Profile;
use App\Models\ProfessionalRegistrationProgress;
use App\Models\User;
use App\Models\UserCertificate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    function set_password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|min:8',
            'scenario' => 'sometimes|in:login,register', // Optional parameter to indicate scenario
            'user_type' => 'sometimes|in:customer,professional', // User type for role validation
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }

        $user = User::where('email', $request->email)->first();

        if ($user) {
            // User exists - this is a login scenario
            if (!Hash::check($request->password, $user->password)) {
                return api_response(false, "Invalid password");
            }

            // Check if user has the correct role for the selected user type
            $userRole = $user->getRoleNames()->first();
            $selectedUserType = $request->user_type;

            if ($selectedUserType && $userRole !== $selectedUserType) {
                return api_response(false, "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            }

            // Login the user
            Auth::login($user);

            return response()->json([
                'status' => true,
                'message' => "Login successful",
                'role' => $userRole,
                'scenario' => 'login'
            ]);
        } else {
            // User doesn't exist - this should not happen in normal flow
            // But we'll handle it as registration scenario
            return api_response(false, "User not found. Please complete email verification first.");
        }
    }

    function registerUserType($user_type)
    {
        $role = auth()->user()->roles->first()->name ?? null;
        if ($role && $role == $user_type) {
            $user = auth()->user();
            if ($user->hasRole("customer")) {
                if (auth()->user()->profile) {
                    return redirect()->route('dashboard');
                }
            }
            $categories = Category::where("status", 1)->get();
            $certifications = Certification::all();
            $holidays = Holiday::where("status", 1)->get();
            return view("auth.register." . $user_type, compact("categories", "certifications",'holidays'));
        } else {
            abort(403, 'Unauthorized action.');
        }
    }

    function registerProfessional(Request $request)
    {
        // Remove debug statement - return $request->all();
        $step_one = [
            'full_name' => 'required',
            'company_name' => 'required',
            'phone' => 'required',
            'website' => 'required|url',
            'facebook' => 'url',
            'instagram' => 'url',
            'tiktok' => 'url',
            "location" => 'required',
            "lat" => 'required',
            "lng" => 'required',
            "location_service" => 'required',
            "company_id" => 'required',
            "vat_number" => 'required',
        ];
        $step_two = [
            'bank_name' => 'required',
            'account_number' => 'required',
            'sort_code' => 'required',
            'iban' => 'required',
            'swift' => 'required',
        ];
        $validator = Validator::make($request->all(), [
            'full_name' => 'required',
            'company_name' => 'required',
            'phone' => 'required',
            'website' => 'required|url',
            'facebook' => 'url',
            'instagram' => 'url',
            'tiktok' => 'url',
            "location" => 'required',
            "lat" => 'required',
            "lng" => 'required',
            "location_service" => 'required',
            "company_id" => 'required',
            "vat_number" => 'required',
        ]);
        if ($validator->fails()) {
            return api_response(false, $validator->errors()->first());
        }
    }

    function registerCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'fullname' => 'required',
            'password' => 'required|min:8',
            'phone' => 'required',
            'services' => 'required',
        ]);
        if ($validator->fails()) {
            return redirect()->back()
                ->with([
                    'error' => 'Validation Error',
                    'message' => $validator->errors()->first(),
                    'type' => 'error',
                ])
                ->withInput();
        }
        $user = User::find(auth()->id());
        $user->name = $request->fullname;
        $user->password = Hash::make($request->password);
        $user->save();
        $profile = $user->profile;
        if ($profile == null) {
            $profile = new Profile();
            $profile->phone = $request->phone;
            $profile->service_preferences = $request->services;
            if (isset($request->avatar)) {
                $profile->pic = $this->storeImage("user-image", $request->file('avatar'));
            } else {
                $profile->pic = 'no_avatar.jpg';
            }
            $profile->save();
        }
        $profile->user_id = $user->id;
        $profile->save();
        return redirect()->route('dashboard')->with([
            'title' => 'Done',
            'message' => 'You have been registered successfully',
            'type' => 'success',
        ]);
    }

    /**
     * Save step data for professional registration
     */
    public function saveStepData(Request $request)
    {
        $user = auth()->user();
        $step = $request->input('step');

        // Debug logging for step 3
        if ($step == 3) {
            Log::info('Step 3 Debug - Raw Request Data:', [
                'all_data' => $request->all(),
                'certificates' => $request->input('certificates'),
                'product_certifications' => $request->input('product_certifications'),
                'files' => $request->allFiles()
            ]);
        }

        // Get or create progress record
        $progress = ProfessionalRegistrationProgress::firstOrCreate(
            ['user_id' => $user->id],
            ['current_step' => 1]
        );

        // Handle file uploads first
        $stepData = $this->handleFileUploads($request, $step);

        // Validate and save step data
        $validatedData = $this->validateStepData($request, $step);

        if ($validatedData === false) {
            return response()->json(['success' => false, 'message' => 'Validation failed']);
        }

        // Merge file upload data with validated data
        $stepData = array_merge($validatedData, $stepData);

        // Debug logging for step 3
        if ($step == 3) {
            Log::info('Step 3 Debug - Final Step Data:', $stepData);
        }

        // Save step data
        $progress->setStepData($step, $stepData);

        // Also save to profile for immediate use
        $this->saveToProfile($user, $step, $stepData);
        return response()->json(['success' => true, 'current_step' => $progress->current_step]);
    }

    /**
     * Get current progress for professional registration
     */
    public function getProgress(Request $request)
    {
        $user = auth()->user();
        $progress = ProfessionalRegistrationProgress::where('user_id', $user->id)->first();

        if ($progress) {
            return response()->json([
                'success' => true,
                'current_step' => $progress->current_step,
                'completed_steps' => $progress->getCompletedStepsCount()
            ]);
        }

        return response()->json(['success' => true, 'current_step' => 1]);
    }

    /**
     * Validate step data based on step number
     */
    private function validateStepData(Request $request, $step)
    {
        $rules = $this->getValidationRules($step);

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return false;
        }

        // For step 3, we need to include all certificate data, not just the keys from rules
        if ($step == 3) {
            $data = $request->only(array_keys($rules));
            // Include all certificate data
            if ($request->has('certificates')) {
                $data['certificates'] = $request->input('certificates');
            }
            return $data;
        }

        return $request->only(array_keys($rules));
    }

    /**
     * Get validation rules for each step
     */
    private function getValidationRules($step)
    {
        switch ($step) {
            case 1:
                return [
                    'full_name' => 'required|string|max:255',
                    'company_name' => 'required|string|max:255',
                    'phone' => 'required|string|max:20',
                    'website' => 'nullable|url',
                    'facebook' => 'nullable|url',
                    'instagram' => 'nullable|url',
                    'tiktok' => 'nullable|url',
                    'location' => 'required|string',
                    // 'lat' => 'required|numeric',
                    // 'lng' => 'required|numeric',
                    'location_service' => 'required|integer|min:1',
                    'company_id' => 'required|string',
                    'vat_number' => 'required|string',
                ];
            case 2:
                return [
                    'subcategories' => 'required|array',
                    // 'category' => 'required|array',
                    // 'category.*.id' => 'required|integer',
                    // 'category.*.subcategory' => 'nullable|array',
                    // 'category.*.subcategory.*' => 'integer',
                    // 'category.*.subcategory' => 'required_if:category.*.id,true|array',
                ];
            case 3:
                return [
                    'product_certifications' => 'nullable|array',
                    'certificates' => 'nullable|array',
                    'certificates.*.title' => 'required|string',
                    'certificates.*.issued_by' => 'required|string',
                    'certificates.*.issued_date' => 'required|date',
                    'certificates.*.end_date' => 'required|date',
                    'certificates.*.image' => 'nullable|mimes:jpeg,png,jpg|max:2048',
                    'certificates.*.exception' => 'nullable|string',
                    "certificates.*.exception_reason" => 'nullable|string|required_if:certificates.*.exception,1',
                ];
            case 4:
                return [
                    'availability' => 'nullable|array',
                    'holidays' => 'nullable|array',
                ];
            case 5:
                return [
                    'banner_image' => 'nullable|string',
                    'gallery_images' => 'nullable|array',
                ];
            default:
                return [];
        }
    }

    /**
     * Save step data to user profile
     */
    private function saveToProfile($user, $step, $data)
    {
        $profile = $user->profile ?? new Profile();
        $profile->user_id = $user->id;

        switch ($step) {
            case 1:
                $user = User::find(auth()->id());
                $user->name = $data['full_name'] ?? null;
                $user->save();
                // $profile->name = $data['full_name'] ?? null;
                $profile->pic =  $data['avatar'];
                $profile->company_name = $data['company_name'] ?? null;
                $profile->phone = $data['phone'] ?? null;
                $profile->website = $data['website'] ?? null;
                $profile->facebook = $data['facebook'] ?? null;
                $profile->instagram = $data['instagram'] ?? null;
                $profile->tiktok = $data['tiktok'] ?? null;
                $profile->location = $data['location'] ?? null;
                $profile->lat = $data['lat'] ?? null;
                $profile->lng = $data['lng'] ?? null;
                $profile->location_service = $data['location_service'] ?? null;
                $profile->company_id = $data['company_id'] ?? null;
                $profile->vat_number = $data['vat_number'] ?? null;
                break;
            case 2:
                $subcategories = $data['subcategories'] ?? [];
                $user->subcategories()->sync($subcategories);
                break;
            case 3:
                Log::info('Step 3 saveToProfile - Input Data:', $data);

                $productCertifications = $data['product_certifications'] ?? [];
                Log::info('Product Certifications to sync:', $productCertifications);
                $user->product_cerficates()->sync($productCertifications);

                // Clear existing certificates for this user to avoid duplicates
                UserCertificate::where('user_id', $user->id)->delete();

                $certifications = $data['certificates'] ?? [];
                Log::info('Certificates to save:', $certifications);

                foreach ($certifications as $index => $certification) {
                    Log::info("Processing certificate {$index}:", $certification);

                    $userCertificate = new UserCertificate();
                    $userCertificate->user_id = $user->id;
                    $userCertificate->title = $certification['title'] ?? null;
                    $userCertificate->issued_by = $certification['issued_by'] ?? null;
                    $userCertificate->issued_date = $certification['issued_date'] ?? null;
                    $userCertificate->end_date = $certification['end_date'] ?? null;

                    // Handle file upload for certificate image
                    if (isset($certification['image']) && $certification['image']) {
                        if (is_string($certification['image'])) {
                            // If it's already a stored path
                            $userCertificate->image = $certification['image'];
                        } else {
                            // If it's a file object, store it
                            $userCertificate->image = $this->storeImage('certificates', $certification['image']);
                        }
                    }

                    $userCertificate->exception = isset($certification['exception']) ? 1 : 0;
                    $userCertificate->exception_reason = $certification['exception_reason'] ?? null;

                    Log::info("Saving certificate:", $userCertificate->toArray());
                    $userCertificate->save();
                    Log::info("Certificate saved with ID: " . $userCertificate->id);
                }
                break;
            case 4:
                $profile->availability = json_encode($data['availability'] ?? []);
                $profile->holidays = json_encode($data['holidays'] ?? []);
                break;
            case 5:
                $profile->banner_image = $data['banner_image'] ?? null;
                $profile->gallery_images = json_encode($data['gallery_images'] ?? []);
                break;
        }

        $profile->save();
    }

    /**
     * Handle file uploads for each step
     */
    private function handleFileUploads(Request $request, $step)
    {
        $uploadedFiles = [];

        switch ($step) {
            case 1:
                // Handle avatar/logo upload
                if ($request->hasFile('avatar')) {
                    $uploadedFiles['avatar'] = $this->storeImage('user-image', $request->file('avatar'));
                }

                // Handle cropped profile image
                if ($request->has('cropped_profile_image')) {
                    // Handle base64 cropped image if needed
                    $uploadedFiles['avatar'] = $this->handleCroppedImage($request->input('cropped_profile_image'));
                }
                break;

            case 3:
                // Handle certificate image uploads
                $certificates = [];
                $certificateIndex = 0;

                // Handle the main certificate form (certificates[0])
                while ($request->hasFile("certificates.{$certificateIndex}.image")) {
                    $certificates[$certificateIndex]['image'] = $request->file("certificates.{$certificateIndex}.image");
                    $certificateIndex++;
                }

                if (!empty($certificates)) {
                    $uploadedFiles['certificates'] = $certificates;
                }
                break;

            case 5:
                // Handle banner image upload
                if ($request->hasFile('banner_image')) {
                    $uploadedFiles['banner_image'] = $this->storeImage('banner-images', $request->file('banner_image'));
                }

                // Handle gallery images
                if ($request->hasFile('gallery_images')) {
                    $galleryImages = [];
                    foreach ($request->file('gallery_images') as $file) {
                        $galleryImages[] = $this->storeImage('gallery-images', $file);
                    }
                    $uploadedFiles['gallery_images'] = $galleryImages;
                }
                break;
        }

        return $uploadedFiles;
    }

    /**
     * Handle cropped image from base64
     */
    private function handleCroppedImage($base64Image)
    {
        if (empty($base64Image)) return null;

        // Decode base64 image
        $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $base64Image));
        $filename = 'cropped_' . time() . '.png';

        // Store the image
        Storage::disk('website')->put('user-image/' . $filename, $imageData);

        return $filename;
    }
}
