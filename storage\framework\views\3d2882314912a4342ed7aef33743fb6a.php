<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Categories</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('categories-create')): ?>
                        <a class="add-btn" id="add-category-button" data-bs-toggle="modal" data-bs-target="#add-category">
                            <i class="fa-solid fa-plus me-3"></i> Add Category
                        </a>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subcategories-create')): ?>
                        <a class="add-btn" id="add-subcategory-button" data-bs-toggle="modal"
                            data-bs-target="#add-sub-category">
                            <i class="fa-solid fa-plus me-3"></i> Add Sub Category
                        </a>
                    <?php endif; ?>
                </div>
                <div class="col-lg-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services" id="category-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-category" type="button" role="tab"
                                aria-controls="pills-category" aria-selected="true">
                                Main Category
                            </button>

                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services" id="subcategory-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-sub-category" type="button" role="tab"
                                aria-controls="pills-group-service" aria-selected="false">
                                Sub Category
                            </button>
                        </li>
                    </ul>


                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-category" role="tabpanel"
                            aria-labelledby="category-tab" tabindex="0">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px">
                                            <i class="fa fa-chevron-down down-arrow  ms-9"></i>
                                        </div>
                                    </label>
                                </div>
                                <table id="responsiveTable" class="responsiveTable display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>CATEGRORY NAME</th>
                                            <th>SUB CATEGRORY</th>
                                            <th>DATE</th>
                                            <th></th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td data-label="">
                                                    <div class="card  flex-row shadow-none p-0 gap-3">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="<?php echo e(asset('website') . '/' . $category->image ?? ''); ?>"
                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                alt="card-image" />
                                                        </div>

                                                    </div>
                                                </td>
                                                <td data-label="CATEGRORY NAME"><?php echo e($category->name ?? ''); ?></td>
                                                <td data-label="SUB CATEGRORY"><?php echo e($category->subcategories->count() ?? ''); ?>

                                                </td>
                                                <td data-label="DATE"><?php echo e($category->created_at->format('d M, Y') ?? ''); ?>

                                                </td>
                                                <td data-label="">
                                                    <div class="toggle-container">
                                                        <label class="switch">
                                                            <!-- Dynamically set checked based on category status -->
                                                            <input type="checkbox" class="toggle-input category-toggle"
                                                                data-category-id="<?php echo e($category->id); ?>"
                                                                <?php echo e($category->status == 1 ? 'checked' : ''); ?>>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span
                                                            class="toggle-label"><?php echo e($category->status == 1 ? 'Active' : 'Deactive'); ?></span>
                                                    </div>
                                                </td>
                                                <td data-label="">
                                                    <a class="purple-btn" data-bs-toggle="modal"
                                                        data-bs-target="#add-sub-category"
                                                        data-category-id="<?php echo e($category->id); ?>">
                                                        <i class="fa-solid fa-plus me-3"></i> Add Sub Category
                                                    </a>
                                                </td>
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('categories-edit')): ?>
                                                                <li>
                                                                    <button
                                                                        class="dropdown-item complete fs-14 regular edit-category"
                                                                        type="button" data-id="<?php echo e($category->ids); ?>">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Edit
                                                                    </button>
                                                                </li>
                                                            <?php endif; ?>
                                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('categories-delete')): ?>
                                                                <li>
                                                                    <?php echo Form::open([
                                                                        'method' => 'DELETE',
                                                                        'route' => ['categories.destroy', $category->ids],
                                                                        'class' => 'delete-form',
                                                                    ]); ?>

                                                                    <a class="dropdown-item cancel fs-14 regular"
                                                                        href="javascript:void(0)"
                                                                        onclick="showDeleteConfirmation(this)">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i> Delete
                                                                    </a>
                                                                    <?php echo Form::close(); ?>

                                                                </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-sub-category" role="tabpanel"
                            aria-labelledby="subcategory-tab" tabindex="1">

                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="customSearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="customSearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="datePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="datePicker" class="datePicker ms-3 w-200px ">
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                </div>
                                <table id="responsiveTable" class="responsiveTable display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>SUB CATEGRORY NAME</th>
                                            <th>MAIN CATEGRORY</th>
                                            <th>DATE</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $subcategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subcategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td data-label="">
                                                    <div class="card  flex-row shadow-none p-0 gap-3">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="<?php echo e(asset('website') . '/' . $subcategory->image ?? ''); ?>"
                                                                class="h-80px w-80px rounded-3 object-fit-contain"
                                                                alt="card-image" />
                                                        </div>
                                                    </div>
                                                </td>
                                                <td data-label="SUB CATEGRORY NAME"><?php echo e($subcategory->name ?? ''); ?></td>
                                                <td data-label="MAIN CATEGRORY">
                                                    <p class="light-blue-badge">
                                                        <?php echo e($subcategory->category->name ?? ''); ?>

                                                    </p>
                                                </td>
                                                <td data-label="DATE">
                                                    <?php echo e($subcategory->created_at->format('d M, Y') ?? ''); ?>

                                                </td>
                                                <td data-label="">
                                                    <div class="toggle-container">
                                                        <label class="switch">
                                                            <!-- Dynamically set checked based on subcategory status -->
                                                            <input type="checkbox" class="toggle-input subcategory-toggle"
                                                                data-subcategory-id="<?php echo e($subcategory->id); ?>"
                                                                <?php echo e($subcategory->status == 1 ? 'checked' : ''); ?>>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span
                                                            class="toggle-label"><?php echo e($subcategory->status == 1 ? 'Active' : 'Deactive'); ?></span>
                                                    </div>
                                                </td>
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subcategories-edit')): ?>
                                                            <li>
                                                                <button class="dropdown-item complete fs-14 regular edit-subcategory"
                                                                    type="button" data-id="<?php echo e($subcategory->ids); ?>">
                                                                    <i class="bi bi-check-circle complete-icon"></i>
                                                                    Edit
                                                                </button>
                                                            </li>
                                                            <?php endif; ?>
                                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subcategories-delete')): ?>
                                                            <li>
                                                                <?php echo Form::open([
                                                                    'method' => 'DELETE',
                                                                    'route' => ['subcategories.destroy', $subcategory->ids],
                                                                    'class' => 'delete-form',
                                                                ]); ?>

                                                                <a class="dropdown-item cancel fs-14 regular" href="javascript:void(0)"
                                                                    onclick="showDeleteConfirmation(this)">
                                                                    <i class="fa-solid fa-xmark cancel-icon"></i> Delete
                                                                </a>
                                                                <?php echo Form::close(); ?>

                                                            </li>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php echo $__env->make('dashboard.admin.categories.modal.add-category-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.admin.categories.modal.edit-category-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.admin.categories.modal.add-sub-category-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.admin.categories.modal.edit-sub-category-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $("#categoryForm").validate({
                rules: {
                    avatar: {
                        required: true
                    },
                    alt_tag: {
                        required: true
                    },
                    image_description: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                    status: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image"
                    },
                    alt_tag: {
                        required: "Please enter alt tag"
                    },
                    image_description: {
                        required: "Please enter image description"
                    },
                    name: {
                        required: "Please enter category name"
                    },
                    status: {
                        required: "Please select status"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            $('.edit-category').click(function() {
                var categoryId = $(this).data('id');
                $.ajax({
                    url: '/categories/' + categoryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#categoryId').val(data.ids);
                        $('#cat_name').val(data.name);
                        $('#cat_status').val(data.status);
                        $('#cat_image_description').val(data.image_description);
                        $('#cat_alt_tag').val(data.alt_tag);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-category').modal('show');
                    },
                });
            });

            $('#editCategoryForm').on('submit', function(e) {
                e.preventDefault();
                var categoryId = $('#categoryId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/categories/' + categoryId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-category').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

        });
    </script>

    <script>
        $(document).ready(function() {
            $(".category-toggle").on('change', function() {
                var isChecked = $(this).prop('checked');
                var categoryId = $(this).data('category-id');
                var newStatus = isChecked ? 1 : 0;

                $.ajax({
                    url: "<?php echo e(route('categories.update-status')); ?>",
                    type: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        category_id: categoryId,
                        status: newStatus
                    },
                    success: function(response) {
                        $(this).next('.toggle-label').text(newStatus === 1 ? 'Active' :
                            'Deactive');
                        // alert('Category status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the category status');
                    }
                });
            });


            $(".subcategory-toggle").on('change', function() {
                var isChecked = $(this).prop('checked');
                var subcategoryId = $(this).data('subcategory-id');
                var newStatus = isChecked ? 1 : 0;
                $.ajax({
                    url: "<?php echo e(route('subcategories.update-status')); ?>",
                    type: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        subcategory_id: subcategoryId,
                        status: newStatus
                    },
                    success: function(response) {
                        $(this).next('.toggle-label').text(newStatus === 1 ? 'Active' :
                            'Deactive');
                        // alert('Subcategory status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the subcategory status');
                    }
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $("#subCategoryForm").validate({
                rules: {
                    avatar: {
                        required: true
                    },
                    alt_image: {
                        required: true
                    },
                    image_description: {
                        required: true
                    },
                    category_id: {
                        required: true
                    },
                    name: {
                        required: true
                    },
                    status: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image"
                    },
                    alt_image: {
                        required: "Please enter alt tag"
                    },
                    image_description: {
                        required: "Please enter image description"
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    name: {
                        required: "Please enter category name"
                    },
                    status: {
                        required: "Please select status"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            $('.edit-subcategory').click(function() {
                var subCategoryId = $(this).data('id');
                $.ajax({
                    url: '/subcategories/' + subCategoryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#subCategoryId').val(data.ids);
                        $('#sub_name').val(data.name);
                        $('#sub_status').val(data.status);
                        $('#sub_image_description').val(data.image_description);
                        $('#sub_alt_tag').val(data.alt_tag);
                        $('#sub_category_id').val(data.category_id);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-sub-category').modal('show');
                    },
                });
            });

            $('#editSubCategoryForm').on('submit', function(e) {
                e.preventDefault();
                var subCategoryId = $('#subCategoryId').val();
                var formData = new FormData(this);
                $.ajax({
                    url: '/subcategories/' + subCategoryId,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        $('#edit-sub-category   ').modal('hide');
                        Swal.fire({
                            icon: response.type,
                            title: response.title,
                            text: response.message
                        });
                        setTimeout(() => {
                            location.reload();
                        }, 1500);
                    },
                    error: function(xhr) {
                        alert('Update failed. Please try again.');
                    }
                });
            });

        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/admin/categories/categories.blade.php ENDPATH**/ ?>